import React, { useState } from 'react';
import { X, TrendingUp, DollarSign, Clock, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';

interface FreeROIAnalysisPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Compelling Free ROI Analysis popup component designed for maximum conversion
 * Features urgency, social proof, and a simple form to capture leads
 */
const FreeROIAnalysisPopup: React.FC<FreeROIAnalysisPopupProps> = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    tradingCapital: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the data to your backend
    console.log('ROI Analysis Request:', formData);
    setIsSubmitted(true);
    
    // Auto-close after 3 seconds
    setTimeout(() => {
      onClose();
      setIsSubmitted(false);
      setFormData({ name: '', email: '', tradingCapital: '' });
    }, 3000);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-2 border-blue-500/30 shadow-2xl animate-in zoom-in-95 duration-300">
        <CardContent className="p-0">
          {/* Header */}
          <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-t-lg">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
            
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                <TrendingUp className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-3xl font-bold text-white mb-2">
                Get Your FREE ROI Analysis
              </h2>
              <p className="text-blue-100 text-lg">
                Discover Your Potential 592% Returns in Under 60 Seconds
              </p>
            </div>
          </div>

          {!isSubmitted ? (
            <div className="p-8">
              {/* Social Proof */}
              <div className="grid grid-cols-3 gap-4 mb-8">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <DollarSign className="w-6 h-6 text-green-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">592%</div>
                  <div className="text-sm text-gray-400">Avg ROI</div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Clock className="w-6 h-6 text-blue-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">48hrs</div>
                  <div className="text-sm text-gray-400">Setup Time</div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Users className="w-6 h-6 text-purple-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">50+</div>
                  <div className="text-sm text-gray-400">Success Stories</div>
                </div>
              </div>

              {/* Urgency Banner */}
              <div className="bg-gradient-to-r from-red-600/20 to-orange-600/20 border border-red-500/30 rounded-lg p-4 mb-6">
                <div className="text-center">
                  <div className="text-red-400 font-semibold mb-1">⚡ LIMITED TIME OFFER</div>
                  <div className="text-white text-sm">
                    Only 12 FREE analyses remaining this month
                  </div>
                </div>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name" className="text-white mb-2 block">
                    Full Name *
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="bg-slate-800 border-slate-600 text-white placeholder:text-gray-400"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <Label htmlFor="email" className="text-white mb-2 block">
                    Email Address *
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="bg-slate-800 border-slate-600 text-white placeholder:text-gray-400"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="tradingCapital" className="text-white mb-2 block">
                    Current Trading Capital
                  </Label>
                  <Input
                    id="tradingCapital"
                    name="tradingCapital"
                    type="text"
                    value={formData.tradingCapital}
                    onChange={handleInputChange}
                    className="bg-slate-800 border-slate-600 text-white placeholder:text-gray-400"
                    placeholder="e.g., $50,000"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-4 text-lg transition-all duration-300 transform hover:scale-105"
                >
                  🚀 Get My FREE ROI Analysis Now
                </Button>
              </form>

              {/* Trust Indicators */}
              <div className="mt-6 text-center">
                <div className="text-xs text-gray-400 mb-2">
                  ✅ No spam, ever  ✅ 100% confidential  ✅ Instant delivery
                </div>
                <div className="text-xs text-gray-500">
                  Join 50+ traders who've already transformed their portfolios
                </div>
              </div>
            </div>
          ) : (
            /* Success State */
            <div className="p-8 text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-green-500/20 rounded-full mb-6">
                <div className="text-4xl">🎉</div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">
                Analysis Request Submitted!
              </h3>
              <p className="text-gray-300 mb-6">
                Your personalized ROI analysis will be sent to <strong>{formData.email}</strong> within the next 24 hours.
              </p>
              <div className="bg-blue-600/20 border border-blue-500/30 rounded-lg p-4">
                <p className="text-blue-300 text-sm">
                  💡 <strong>Pro Tip:</strong> Check your email for exclusive trading strategies while you wait!
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FreeROIAnalysisPopup;
