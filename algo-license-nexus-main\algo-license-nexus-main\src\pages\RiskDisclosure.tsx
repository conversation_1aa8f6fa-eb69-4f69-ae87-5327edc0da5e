import { Layout } from "@/components/Layout";
import { <PERSON>Header } from "@/components/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertTriangle, TrendingDown, Shield, DollarSign, Clock, Target, Brain, FileText } from "lucide-react";
import { Link } from "react-router-dom";

/**
 * Risk Disclosure page component providing comprehensive risk warnings
 * Features detailed risk disclosures required for financial services
 * Maintains professional legal document formatting with clear warnings
 */
const RiskDisclosure = () => {
  const riskCategories = [
    {
      id: "market-risk",
      title: "Market Risk",
      icon: TrendingDown,
      color: "text-red-400",
      risks: [
        "Trading involves substantial risk of loss and may not be suitable for all investors.",
        "Past performance does not guarantee future results.",
        "Market volatility can result in rapid and substantial losses.",
        "Economic events, political instability, and market sentiment can significantly impact performance.",
        "Leverage amplifies both potential gains and losses."
      ]
    },
    {
      id: "technology-risk",
      title: "Technology Risk",
      icon: Brain,
      color: "text-orange-400",
      risks: [
        "AI algorithms may not perform as expected in all market conditions.",
        "System failures, connectivity issues, or software bugs can impact trading performance.",
        "Algorithmic strategies may become less effective as market conditions change.",
        "Technology infrastructure dependencies may create operational risks.",
        "Data feed interruptions or delays can affect strategy performance."
      ]
    },
    {
      id: "liquidity-risk",
      title: "Liquidity Risk",
      icon: DollarSign,
      color: "text-yellow-400",
      risks: [
        "Inability to execute trades at desired prices due to market conditions.",
        "Large positions may be difficult to liquidate quickly without price impact.",
        "Market disruptions can severely limit trading opportunities.",
        "Bid-ask spreads may widen during volatile periods, increasing transaction costs.",
        "Some strategies may require specific market conditions to be effective."
      ]
    },
    {
      id: "operational-risk",
      title: "Operational Risk",
      icon: Shield,
      color: "text-blue-400",
      risks: [
        "Human error in strategy implementation or parameter configuration.",
        "Regulatory changes may impact strategy effectiveness or legality.",
        "Counterparty risk with brokers, exchanges, or other service providers.",
        "Cybersecurity threats and data breaches.",
        "Business continuity risks affecting service availability."
      ]
    }
  ];

  const importantNotices = [
    {
      title: "No Guarantee of Profits",
      description: "There is no guarantee that any strategy will be profitable or will not result in losses."
    },
    {
      title: "Suitability Assessment",
      description: "You should carefully consider whether algorithmic trading is suitable for your financial situation and risk tolerance."
    },
    {
      title: "Professional Advice",
      description: "Consider seeking independent financial, legal, and tax advice before implementing any trading strategy."
    },
    {
      title: "Regulatory Compliance",
      description: "Ensure compliance with all applicable laws and regulations in your jurisdiction."
    }
  ];

  return (
    <Layout>
      <PageHeader
        title="Risk Disclosure"
        subtitle="Important Risk Information"
        description="Please read this risk disclosure carefully before using our AI trading strategies. Trading involves substantial risk and may not be suitable for all investors."
        icon={AlertTriangle}
      />

      {/* Warning Banner */}
      <section className="py-12 px-6">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-red-500/10 border-red-500/30">
            <CardContent className="p-8">
              <div className="flex items-start space-x-4">
                <AlertTriangle className="w-12 h-12 text-red-400 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-2xl font-bold text-white mb-4">High Risk Investment Warning</h3>
                  <p className="text-gray-300 text-lg leading-relaxed mb-4">
                    Algorithmic trading strategies involve substantial risk of loss. You should only invest money
                    that you can afford to lose. Past performance is not indicative of future results.
                  </p>
                  <p className="text-red-300 font-semibold">
                    By using our services, you acknowledge that you understand and accept these risks.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Risk Categories */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Risk <span className="bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent">Categories</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Understanding the various types of risks associated with algorithmic trading strategies.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {riskCategories.map((category) => (
              <Card key={category.id} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardContent className="p-8">
                  <div className="flex items-center space-x-3 mb-6">
                    <category.icon className={`w-8 h-8 ${category.color}`} />
                    <h3 className="text-2xl font-bold text-white">{category.title}</h3>
                  </div>
                  <div className="space-y-3">
                    {category.risks.map((risk, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                        <p className="text-gray-300 leading-relaxed">{risk}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Important Notices */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Important <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">Notices</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Key considerations before implementing any algorithmic trading strategy.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {importantNotices.map((notice, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-3">{notice.title}</h3>
                  <p className="text-gray-400 leading-relaxed">{notice.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Regulatory Information */}
      <section className="py-24 px-6">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-white/5 backdrop-blur-sm border-white/10">
            <CardContent className="p-8">
              <div className="flex items-center space-x-3 mb-6">
                <FileText className="w-8 h-8 text-blue-400" />
                <h2 className="text-2xl font-bold text-white">Regulatory Information</h2>
              </div>
              <div className="space-y-4 text-gray-300 leading-relaxed">
                <p>
                  FoxAI is a small technology company providing algorithmic trading strategies. Our strategies are provided for
                  qualified clients and are subject to various regulatory requirements.
                </p>
                <p>
                  This risk disclosure is provided in compliance with applicable securities laws and regulations.
                  Additional disclosures may be required based on your jurisdiction and regulatory status.
                </p>
                <p>
                  For specific regulatory questions or compliance requirements, please consult with your
                  legal and compliance teams or contact our compliance department.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Questions About <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Risk?</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Our risk management team is available to discuss specific risk considerations for your organization.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/contact"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
                Contact Risk Management
              </Button>
            </Link>
            <Link
              to="/documentation"
              onClick={() => {
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button variant="outline" className="border-white/30 text-white hover:bg-white/10 px-8 py-3 rounded-full transition-all duration-300 hover:scale-105">
                View Documentation
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default RiskDisclosure;
