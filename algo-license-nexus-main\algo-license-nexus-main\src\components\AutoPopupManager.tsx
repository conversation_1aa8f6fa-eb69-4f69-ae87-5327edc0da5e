import React, { useState, useEffect, useCallback } from 'react';
import FreeROIAnalysisPopup from './FreeROIAnalysisPopup';

/**
 * AutoPopupManager - Handles automatic random popup display
 * Features intelligent timing, user behavior tracking, and conversion optimization
 * Ensures popups appear at optimal moments without being intrusive
 */
const AutoPopupManager: React.FC = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [lastShown, setLastShown] = useState<number>(0);
  const [userInteracted, setUserInteracted] = useState(false);
  const [pageViews, setPageViews] = useState(0);
  const [timeOnPage, setTimeOnPage] = useState(0);
  const [hasSeenPopup, setHasSeenPopup] = useState(false);

  // Configuration for popup timing (optimized for conversion)
  const POPUP_CONFIG = {
    minInterval: 30000, // Minimum 30 seconds between popups
    maxInterval: 120000, // Maximum 2 minutes between popups
    minTimeOnPage: 20000, // Show only after 20 seconds on page
    maxPopupsPerSession: 4, // Maximum popups per session
    cooldownAfterClose: 90000, // 1.5 minutes cooldown after user closes
    exitIntentChance: 0.7, // 70% chance to show on exit intent
    highIntentChance: 0.4, // 40% chance to show on high-intent actions
    storageKey: 'roiPopupSeen', // LocalStorage key for tracking popup views
  };

  // Check if user has already seen the popup (persistent across sessions)
  useEffect(() => {
    const hasSeenBefore = localStorage.getItem(POPUP_CONFIG.storageKey) === 'true';
    setHasSeenPopup(hasSeenBefore);

    if (hasSeenBefore) {
      console.log('🚫 User has already seen ROI popup - will not show again');
    }
  }, []);

  // Track user interactions to determine engagement
  useEffect(() => {
    const handleUserInteraction = () => {
      setUserInteracted(true);
    };

    const events = ['click', 'scroll', 'mousemove', 'keydown'];
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  }, []);

  // Track time on page
  useEffect(() => {
    const startTime = Date.now();
    const interval = setInterval(() => {
      setTimeOnPage(Date.now() - startTime);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Track page views
  useEffect(() => {
    setPageViews(prev => prev + 1);
  }, []);

  // Get random interval within configured range
  const getRandomInterval = useCallback(() => {
    const { minInterval, maxInterval } = POPUP_CONFIG;
    return Math.random() * (maxInterval - minInterval) + minInterval;
  }, []);

  // Check if popup should be shown based on various conditions
  const shouldShowPopup = useCallback(() => {
    const now = Date.now();
    const timeSinceLastShown = now - lastShown;
    const popupsShown = parseInt(sessionStorage.getItem('popupsShown') || '0');
    const lastClosedTime = parseInt(sessionStorage.getItem('lastPopupClosed') || '0');
    const timeSinceLastClosed = now - lastClosedTime;

    // MOST IMPORTANT: Don't show if user has already seen the popup (ever)
    if (hasSeenPopup) return false;

    // Don't show if popup is already open
    if (showPopup) return false;

    // Don't show if max popups per session reached
    if (popupsShown >= POPUP_CONFIG.maxPopupsPerSession) return false;

    // Don't show if in cooldown period after user closed popup
    if (lastClosedTime && timeSinceLastClosed < POPUP_CONFIG.cooldownAfterClose) return false;

    // Don't show if minimum time on page hasn't passed
    if (timeOnPage < POPUP_CONFIG.minTimeOnPage) return false;

    // Don't show if minimum interval hasn't passed
    if (timeSinceLastShown < POPUP_CONFIG.minInterval) return false;

    // Show if user has interacted and conditions are met
    return userInteracted;
  }, [showPopup, lastShown, timeOnPage, userInteracted, hasSeenPopup]);

  // Main popup scheduling logic
  useEffect(() => {
    if (!shouldShowPopup()) return;

    const scheduleNextPopup = () => {
      const interval = getRandomInterval();

      setTimeout(() => {
        if (shouldShowPopup()) {
          setShowPopup(true);
          setLastShown(Date.now());
          setHasSeenPopup(true);

          // Mark popup as seen permanently in localStorage
          localStorage.setItem(POPUP_CONFIG.storageKey, 'true');

          // Track popup shown in session storage
          const popupsShown = parseInt(sessionStorage.getItem('popupsShown') || '0');
          sessionStorage.setItem('popupsShown', (popupsShown + 1).toString());

          console.log('🎯 Auto-popup triggered after', interval / 1000, 'seconds - marked as seen permanently');
        }

        // Schedule next popup
        scheduleNextPopup();
      }, interval);
    };

    // Initial scheduling
    const initialDelay = Math.max(
      POPUP_CONFIG.minTimeOnPage - timeOnPage,
      getRandomInterval() * 0.3 // Start with shorter initial delay
    );

    setTimeout(scheduleNextPopup, initialDelay);
  }, [shouldShowPopup, getRandomInterval]);

  // Handle popup close
  const handleClosePopup = useCallback(() => {
    setShowPopup(false);
    sessionStorage.setItem('lastPopupClosed', Date.now().toString());
    console.log('🚫 Popup closed by user - entering cooldown period');
  }, []);

  // Enhanced popup trigger for high-intent actions
  useEffect(() => {
    const handleHighIntentActions = (event: Event) => {
      const target = event.target as HTMLElement;

      // Trigger popup on high-intent actions (pricing page, contact attempts, etc.)
      const highIntentSelectors = [
        '[href*="pricing"]',
        '[href*="contact"]',
        '[href*="checkout"]',
        'button[type="submit"]',
        '.cta-button',
        '.pricing-button'
      ];

      const isHighIntent = highIntentSelectors.some(selector =>
        target.matches(selector) || target.closest(selector)
      );

      if (isHighIntent && shouldShowPopup() && Math.random() < POPUP_CONFIG.highIntentChance) {
        // Configurable chance to show popup on high-intent actions
        setTimeout(() => {
          if (shouldShowPopup()) {
            setShowPopup(true);
            setLastShown(Date.now());
            setHasSeenPopup(true);

            // Mark popup as seen permanently in localStorage
            localStorage.setItem(POPUP_CONFIG.storageKey, 'true');

            console.log('🎯 High-intent popup triggered - marked as seen permanently');
          }
        }, 2000); // Small delay to not interrupt the action
      }
    };

    document.addEventListener('click', handleHighIntentActions);
    return () => document.removeEventListener('click', handleHighIntentActions);
  }, [shouldShowPopup]);

  // Exit-intent detection (when user moves mouse to top of page)
  useEffect(() => {
    const handleExitIntent = (event: MouseEvent) => {
      if (event.clientY <= 50 && shouldShowPopup() && Math.random() < POPUP_CONFIG.exitIntentChance) {
        // Configurable chance to show popup on exit intent
        setShowPopup(true);
        setLastShown(Date.now());
        setHasSeenPopup(true);

        // Mark popup as seen permanently in localStorage
        localStorage.setItem(POPUP_CONFIG.storageKey, 'true');

        console.log('🚪 Exit-intent popup triggered - marked as seen permanently');
      }
    };

    document.addEventListener('mouseleave', handleExitIntent);
    return () => document.removeEventListener('mouseleave', handleExitIntent);
  }, [shouldShowPopup]);

  // Listen for manual popup triggers (from buttons, etc.)
  useEffect(() => {
    const handleManualTrigger = () => {
      // Manual triggers always show the popup, even if user has seen it before
      // This allows the "Get Free ROI Analysis" button to always work
      setShowPopup(true);
      setLastShown(Date.now());
      setHasSeenPopup(true);

      // Mark popup as seen permanently in localStorage
      localStorage.setItem(POPUP_CONFIG.storageKey, 'true');

      console.log('🎯 Manual popup triggered - marked as seen permanently');
    };

    window.addEventListener('triggerROIPopup', handleManualTrigger);
    return () => window.removeEventListener('triggerROIPopup', handleManualTrigger);
  }, []);

  return (
    <>
      <FreeROIAnalysisPopup
        isOpen={showPopup}
        onClose={handleClosePopup}
      />

      {/* Debug info in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 bg-black/80 text-white p-2 rounded text-xs z-50">
          <div>Time on page: {Math.floor(timeOnPage / 1000)}s</div>
          <div>User interacted: {userInteracted ? 'Yes' : 'No'}</div>
          <div>Has seen popup: {hasSeenPopup ? 'Yes' : 'No'}</div>
          <div>Popups shown: {sessionStorage.getItem('popupsShown') || '0'}</div>
          <div>Can show popup: {shouldShowPopup() ? 'Yes' : 'No'}</div>
          <button
            onClick={() => {
              localStorage.removeItem(POPUP_CONFIG.storageKey);
              setHasSeenPopup(false);
              console.log('🔄 Reset popup seen status for testing');
            }}
            className="mt-2 bg-red-600 px-2 py-1 rounded text-xs"
          >
            Reset Popup Status
          </button>
        </div>
      )}
    </>
  );
};

export default AutoPopupManager;
