import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HashRouter, Routes, Route } from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import { NotificationProvider } from "@/components/ui/notification";
import { AccessibilityProvider, SkipLink, KeyboardNavigation } from "@/components/ui/accessibility";
import AutoPopupManager from "./components/AutoPopupManager";
import Index from "./pages/Index";
import About from "./pages/About";
import Services from "./pages/Services";
import PricingPage from "./pages/PricingPage";
import Documentation from "./pages/Documentation";
import ContactPage from "./pages/ContactPage";
import Checkout from "./pages/Checkout";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import RiskDisclosure from "./pages/RiskDisclosure";
import ApiStatus from "./pages/ApiStatus";
import SupportPortal from "./pages/SupportPortal";
import ReleaseNotes from "./pages/ReleaseNotes";
import CommunityForum from "./pages/CommunityForum";
import NotFound from "./pages/NotFound";

/**
 * Main App component with enhanced accessibility and user experience features
 * Provides global providers including notifications, accessibility, and routing
 * Includes comprehensive routing for business website structure with UX optimizations
 */
const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AccessibilityProvider>
      <NotificationProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <HashRouter>
            {/* Skip link for keyboard navigation accessibility */}
            <SkipLink href="#main-content">
              Skip to main content
            </SkipLink>

            <ScrollToTop />
            <Routes>
          {/* Main Pages */}
          <Route path="/" element={<Index />} />
          <Route path="/about" element={<About />} />
          <Route path="/services" element={<Services />} />
          <Route path="/pricing" element={<PricingPage />} />
          <Route path="/documentation" element={<Documentation />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/checkout" element={<Checkout />} />

          {/* Legal Pages */}
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />
          <Route path="/terms-of-service" element={<TermsOfService />} />
          <Route path="/risk-disclosure" element={<RiskDisclosure />} />

          {/* Support & Documentation Pages */}
          <Route path="/api-status" element={<ApiStatus />} />
          <Route path="/support-portal" element={<SupportPortal />} />
          <Route path="/release-notes" element={<ReleaseNotes />} />
          <Route path="/community-forum" element={<CommunityForum />} />

              {/* Catch-all route for 404 - MUST BE LAST */}
              <Route path="*" element={<NotFound />} />
            </Routes>

            {/* Keyboard navigation help overlay */}
            <KeyboardNavigation />

            {/* Global Auto Popup Manager for ROI Analysis */}
            <AutoPopupManager />
          </HashRouter>
        </TooltipProvider>
      </NotificationProvider>
    </AccessibilityProvider>
  </QueryClientProvider>
);

export default App;
