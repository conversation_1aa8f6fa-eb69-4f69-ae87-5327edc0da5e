import { Layout } from "@/components/Layout";
import { <PERSON>Header } from "@/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Users,
  Award,
  Target,
  TrendingUp,
  Shield,
  Brain,
  ArrowRight,
  CheckCircle
} from "lucide-react";
import { <PERSON> } from "react-router-dom";

/**
 * About page component showcasing company information, team, and values
 * Features company story, team profiles, achievements, and mission statement
 * Maintains consistent styling with the overall site design
 */
const About = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Chief Executive Officer & Founder",
      expertise: "AI Strategy & Business Development",
      experience: "8+ years",
      background: "Entrepreneur and AI Trading Specialist"
    },
    {
      name: "<PERSON>",
      role: "Head of Strategy Development",
      expertise: "Algorithmic Trading Systems",
      experience: "12+ years",
      background: "Ex-Renaissance Technologies Senior Developer"
    },
    {
      name: "<PERSON><PERSON> <PERSON><PERSON>",
      role: "Chief Data Scientist",
      expertise: "Machine Learning & Predictive Analytics",
      experience: "10+ years",
      background: "Former Two Sigma Research Scientist"
    }
  ];

  const achievements = [
    {
      icon: TrendingUp,
      title: "592% Average ROI",
      description: "Across all licensed strategies in 2023"
    },
    {
      icon: Shield,
      title: "Zero Security Breaches",
      description: "Maintaining perfect security record since inception"
    },
    {
      icon: Award,
      title: "5+ Patents",
      description: "In AI and algorithmic trading technologies"
    },
    {
      icon: Users,
      title: "50+ Clients",
      description: "Trusted by individual traders globally"
    }
  ];

  const values = [
    {
      title: "Innovation Excellence",
      description: "Pushing the boundaries of AI and algorithmic trading through cutting-edge research and development."
    },
    {
      title: "Exclusive Partnership",
      description: "Building long-term relationships with select clients to ensure competitive advantages."
    },
    {
      title: "Rigorous Testing",
      description: "Every strategy undergoes extensive backtesting and real-world validation before licensing."
    },
    {
      title: "Continuous Evolution",
      description: "Constantly refining and updating our algorithms to adapt to changing market conditions."
    }
  ];

  return (
    <Layout>
      <PageHeader
        title="About Our Mission"
        subtitle="Elite AI Research"
        description="We are a team of world-class AI researchers and quantitative analysts dedicated to developing the most sophisticated algorithmic trading strategies available exclusively to elite financial institutions."
        icon={Brain}
      />

      {/* Company Story Section */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Our <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Story</span>
              </h2>
              <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                Founded in 2019 by a team of former Wall Street quantitative researchers and Silicon Valley AI scientists,
                AI Strategy Licensing emerged from a simple observation: the most profitable trading strategies were being
                hoarded by a select few institutions.
              </p>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                We set out to democratize access to elite-level algorithmic strategies while maintaining the exclusivity
                that ensures our clients' competitive advantage. Today, we license our proprietary AI-driven strategies
                to carefully selected financial institutions worldwide.
              </p>
              <Link
                to="/services"
                onClick={() => {
                  setTimeout(() => {
                    window.scrollTo({ top: 0, behavior: 'instant' });
                  }, 10);
                }}
              >
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
                  Learn About Our Process
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
            </div>

            <div className="grid grid-cols-2 gap-6">
              {achievements.map((achievement, index) => (
                <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <achievement.icon className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-xl font-bold text-white mb-2">{achievement.title}</h3>
                    <p className="text-gray-400 text-sm">{achievement.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Leadership <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Team</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our team combines decades of experience from the world's most prestigious financial institutions and technology companies.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105">
                <CardHeader className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Users className="w-10 h-10 text-white" />
                  </div>
                  <CardTitle className="text-xl text-white">{member.name}</CardTitle>
                  <CardDescription className="text-blue-400 font-semibold">{member.role}</CardDescription>
                </CardHeader>
                <CardContent className="text-center space-y-3">
                  <Badge variant="secondary" className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-300 border-blue-500/30">
                    {member.expertise}
                  </Badge>
                  <p className="text-gray-400 text-sm">{member.experience} experience</p>
                  <p className="text-gray-300 text-sm">{member.background}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Values</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              The principles that guide our research, development, and client relationships.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <div key={index} className="flex items-start space-x-4 p-6 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 hover:bg-white/10 transition-all duration-300">
                <CheckCircle className="w-8 h-8 text-green-400 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-xl font-semibold text-white mb-3">{value.title}</h3>
                  <p className="text-gray-400 leading-relaxed">{value.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default About;
