import { Layout } from "@/components/Layout";
import { <PERSON>Header } from "@/components/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Check,
  Crown,
  Star,
  Zap,
  DollarSign,
  Shield,
  Phone,
  Mail,
  Clock,
  Users,
  TrendingUp
} from "lucide-react";
import { Link } from "react-router-dom";

/**
 * Pricing page component showcasing detailed pricing tiers and features
 * Features comprehensive comparison tables, add-on services, and enterprise options
 * Includes clear value propositions and contact information for each tier
 */
const PricingPage = () => {
  const plans = [
    {
      name: "Select",
      price: "$5,000",
      period: "per strategy",
      description: "Perfect for emerging funds and boutique firms looking to enhance their trading capabilities",
      icon: Star,
      color: "from-blue-500 to-cyan-500",
      features: [
        "Access to Tier 2 strategies",
        "Basic implementation support (email)",
        "Quarterly performance reports",
        "Standard documentation package",
        "1-year license term",
        "Basic parameter customization",
        "Community forum access"
      ],
      limitations: [
        "No access to Tier 1 strategies",
        "Limited customization options",
        "No phone support"
      ],
      popular: false,
      cta: "Start with Select"
    },
    {
      name: "Elite",
      price: "$15,000",
      period: "per strategy",
      description: "For established institutions seeking premium algorithms with comprehensive support",
      icon: Crown,
      color: "from-purple-500 to-pink-500",
      features: [
        "Access to Tier 1 & 2 strategies",
        "Dedicated implementation team",
        "Monthly performance analysis",
        "Priority phone & email support",
        "Custom parameter tuning",
        "Advanced documentation suite",
        "2-year license term",
        "Risk management consultation",
        "Strategy combination guidance"
      ],
      limitations: [
        "Standard SLA response times",
        "Limited white-label options"
      ],
      popular: true,
      cta: "Choose Elite"
    },
    {
      name: "Sovereign",
      price: "Custom",
      period: "enterprise pricing",
      description: "Exclusive partnerships for market leaders requiring bespoke solutions",
      icon: Zap,
      color: "from-gold-400 to-yellow-500",
      features: [
        "Exclusive strategy development",
        "Full white-label licensing",
        "Dedicated research team",
        "24/7 premium support",
        "Real-time performance monitoring",
        "Unlimited license term",
        "IP co-development rights",
        "Custom integration services",
        "Regulatory compliance support",
        "Executive advisory access"
      ],
      limitations: [],
      popular: false,
      cta: "Contact Sales"
    }
  ];

  const addOnServices = [
    {
      name: "Advanced Analytics Dashboard",
      price: "$2,500",
      period: "one-time setup",
      description: "Real-time performance monitoring with custom KPI tracking"
    },
    {
      name: "Risk Management Module",
      price: "$1,500",
      period: "per year",
      description: "Enhanced risk controls and stress testing capabilities"
    },
    {
      name: "API Integration Package",
      price: "$3,500",
      period: "one-time",
      description: "Custom API development for seamless system integration"
    },
    {
      name: "Training & Certification",
      price: "$1,000",
      period: "per team",
      description: "Comprehensive training program for your trading team"
    }
  ];

  const comparisonFeatures = [
    { feature: "Strategy Access", select: "Tier 2 Only", elite: "Tier 1 & 2", sovereign: "All + Exclusive" },
    { feature: "Implementation Support", select: "Email Only", elite: "Dedicated Team", sovereign: "24/7 Premium" },
    { feature: "Performance Reports", select: "Quarterly", elite: "Monthly", sovereign: "Real-time" },
    { feature: "Customization Level", select: "Basic", elite: "Advanced", sovereign: "Unlimited" },
    { feature: "License Term", select: "1 Year", elite: "2 Years", sovereign: "Unlimited" },
    { feature: "SLA Response Time", select: "48 Hours", elite: "24 Hours", sovereign: "1 Hour" },
    { feature: "White-label Rights", select: "❌", elite: "Limited", sovereign: "Full Rights" },
    { feature: "IP Co-development", select: "❌", elite: "❌", sovereign: "✅" }
  ];

  return (
    <Layout>
      <PageHeader
        title="Investment Tiers"
        subtitle="Premium Licensing"
        description="Choose the licensing tier that matches your firm's ambitions and scale. All plans include comprehensive due diligence documentation and compliance support."
        icon={DollarSign}
      />

      {/* Pricing Cards */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {plans.map((plan, index) => (
              <Card key={index} className={`bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105 relative ${plan.popular ? 'ring-2 ring-purple-500 ring-opacity-50' : ''}`}>
                {plan.popular && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1">
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-8">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${plan.color} flex items-center justify-center`}>
                    <plan.icon className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl text-white mb-2">{plan.name}</CardTitle>
                  <CardDescription className="text-gray-400 mb-4 min-h-[3rem]">{plan.description}</CardDescription>
                  <div className="text-center">
                    <span className="text-4xl font-bold text-white">{plan.price}</span>
                    <span className="text-gray-400 text-lg ml-2">{plan.period}</span>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    {plan.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center text-gray-300">
                        <Check className="w-5 h-5 text-green-400 mr-3 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {plan.limitations.length > 0 && (
                    <>
                      <Separator className="bg-white/10" />
                      <div className="space-y-2">
                        <h4 className="text-gray-400 text-sm font-medium">Limitations:</h4>
                        {plan.limitations.map((limitation, idx) => (
                          <div key={idx} className="flex items-center text-gray-500">
                            <span className="w-5 h-5 mr-3 flex-shrink-0 text-center">•</span>
                            <span className="text-sm">{limitation}</span>
                          </div>
                        ))}
                      </div>
                    </>
                  )}

                  <Link
                    to="/checkout"
                    onClick={() => {
                      setTimeout(() => {
                        window.scrollTo({ top: 0, behavior: 'instant' });
                      }, 10);
                    }}
                  >
                    <Button className={`w-full mt-8 bg-gradient-to-r ${plan.color} hover:opacity-90 text-white font-semibold py-3 rounded-xl transition-all duration-300`}>
                      {plan.cta}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Comparison Table */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Detailed <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Comparison</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Compare features across all licensing tiers to find the perfect fit for your organization.
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10">
              <thead>
                <tr className="border-b border-white/10">
                  <th className="text-left p-6 text-white font-semibold">Feature</th>
                  <th className="text-center p-6 text-blue-400 font-semibold">Select</th>
                  <th className="text-center p-6 text-purple-400 font-semibold">Elite</th>
                  <th className="text-center p-6 text-yellow-400 font-semibold">Sovereign</th>
                </tr>
              </thead>
              <tbody>
                {comparisonFeatures.map((row, index) => (
                  <tr key={index} className="border-b border-white/5 hover:bg-white/5 transition-colors">
                    <td className="p-6 text-gray-300 font-medium">{row.feature}</td>
                    <td className="p-6 text-center text-gray-400">{row.select}</td>
                    <td className="p-6 text-center text-gray-400">{row.elite}</td>
                    <td className="p-6 text-center text-gray-400">{row.sovereign}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* Add-on Services */}
      <section className="py-24 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Add-on <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Services</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Enhance your licensing package with additional services tailored to your specific needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {addOnServices.map((service, index) => (
              <Card key={index} className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl text-white">{service.name}</CardTitle>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-400">{service.price}</div>
                      <div className="text-sm text-gray-500">{service.period}</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-400">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 px-6 bg-slate-900/50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Get Started?</span>
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Contact our team to discuss your specific requirements and receive a customized proposal.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="flex flex-col items-center">
              <Phone className="w-12 h-12 text-blue-400 mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Phone</h3>
              <p className="text-gray-400">+4740396880</p>
            </div>
            <div className="flex flex-col items-center">
              <Mail className="w-12 h-12 text-purple-400 mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Email</h3>
              <p className="text-gray-400"><EMAIL></p>
            </div>
            <div className="flex flex-col items-center">
              <Clock className="w-12 h-12 text-green-400 mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Response Time</h3>
              <p className="text-gray-400">Within 24 hours</p>
            </div>
          </div>

          <Link
            to="/contact"
            onClick={() => {
              setTimeout(() => {
                window.scrollTo({ top: 0, behavior: 'instant' });
              }, 10);
            }}
          >
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 text-lg rounded-full transition-all duration-300 hover:scale-105 shadow-lg">
              Schedule Consultation
            </Button>
          </Link>
        </div>
      </section>
    </Layout>
  );
};

export default PricingPage;
