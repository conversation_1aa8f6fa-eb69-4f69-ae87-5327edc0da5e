import { Layout } from "@/components/Layout";
import { <PERSON>Header } from "@/components/PageHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Shield, Lock, Eye, Database, UserCheck, AlertTriangle } from "lucide-react";

/**
 * Privacy Policy page component providing comprehensive privacy information
 * Features detailed sections on data collection, usage, and protection
 * Maintains professional legal document formatting with clear navigation
 */
const PrivacyPolicy = () => {
  const sections = [
    {
      id: "information-collection",
      title: "Information We Collect",
      icon: Database,
      content: [
        "Personal Information: Name, email address, phone number, job title, and company information when you contact us or request our services.",
        "Technical Information: IP addresses, browser type, operating system, and usage patterns when you visit our website.",
        "Financial Information: Trading performance data, risk metrics, and portfolio information as required for strategy implementation and monitoring.",
        "Communication Records: Records of our communications with you, including emails, phone calls, and meeting notes."
      ]
    },
    {
      id: "information-use",
      title: "How We Use Your Information",
      icon: User<PERSON>heck,
      content: [
        "Service Delivery: To provide, maintain, and improve our AI strategy licensing services.",
        "Communication: To respond to your inquiries, provide customer support, and send important service updates.",
        "Performance Monitoring: To monitor strategy performance and provide reporting as outlined in your licensing agreement.",
        "Compliance: To meet regulatory requirements and maintain audit trails as required by financial services regulations.",
        "Research and Development: To improve our algorithms and develop new strategies (using anonymized and aggregated data only)."
      ]
    },
    {
      id: "information-sharing",
      title: "Information Sharing and Disclosure",
      icon: Eye,
      content: [
        "We do not sell, trade, or otherwise transfer your personal information to third parties without your explicit consent, except as outlined below:",
        "Service Providers: We may share information with trusted third-party service providers who assist us in operating our business, subject to strict confidentiality agreements.",
        "Legal Requirements: We may disclose information when required by law, regulation, or legal process.",
        "Business Transfers: In the event of a merger, acquisition, or sale of assets, customer information may be transferred as part of the transaction.",
        "Consent: We may share information with your explicit consent for specific purposes."
      ]
    },
    {
      id: "data-security",
      title: "Data Security",
      icon: Lock,
      content: [
        "Encryption: All data is encrypted in transit and at rest using industry-standard encryption protocols (AES-256).",
        "Access Controls: Strict access controls ensure only authorized personnel can access your information on a need-to-know basis.",
        "Infrastructure Security: Our systems are hosted on secure, SOC 2 Type II certified infrastructure with 24/7 monitoring.",
        "Regular Audits: We conduct regular security audits and penetration testing to identify and address potential vulnerabilities.",
        "Incident Response: We maintain a comprehensive incident response plan to address any potential security breaches promptly."
      ]
    },
    {
      id: "data-retention",
      title: "Data Retention",
      icon: Database,
      content: [
        "Personal Information: Retained for the duration of our business relationship and for 7 years thereafter as required by financial services regulations.",
        "Trading Data: Performance and trading data is retained for the duration of the licensing agreement and for 10 years thereafter for regulatory compliance.",
        "Communication Records: Retained for 7 years from the date of the last communication.",
        "Website Analytics: Anonymous usage data is retained for 2 years for website improvement purposes."
      ]
    },
    {
      id: "your-rights",
      title: "Your Rights",
      icon: Shield,
      content: [
        "Access: You have the right to request access to the personal information we hold about you.",
        "Correction: You may request correction of any inaccurate or incomplete personal information.",
        "Deletion: You may request deletion of your personal information, subject to our legal and regulatory obligations.",
        "Portability: You have the right to receive your personal information in a structured, machine-readable format.",
        "Objection: You may object to certain processing of your personal information.",
        "Withdrawal of Consent: You may withdraw consent for processing where we rely on consent as the legal basis."
      ]
    }
  ];

  const lastUpdated = "December 15, 2024";

  return (
    <Layout>
      <PageHeader
        title="Privacy Policy"
        subtitle="Data Protection"
        description="We are committed to protecting your privacy and ensuring the security of your personal and financial information. This policy outlines how we collect, use, and protect your data."
        icon={Shield}
      />

      {/* Last Updated */}
      <section className="py-12 px-6">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-blue-500/10 border-blue-500/20">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-6 h-6 text-blue-400" />
                <div>
                  <h3 className="text-lg font-semibold text-white">Last Updated</h3>
                  <p className="text-gray-300">This Privacy Policy was last updated on {lastUpdated}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Privacy Policy Content */}
      <section className="py-24 px-6">
        <div className="max-w-4xl mx-auto space-y-12">
          {/* Introduction */}
          <Card className="bg-white/5 backdrop-blur-sm border-white/10">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Introduction</h2>
              <div className="prose prose-invert max-w-none">
                <p className="text-gray-300 leading-relaxed mb-4">
                  AI Strategy Licensing ("we," "our," or "us") is committed to protecting the privacy and security
                  of our clients' personal and financial information. This Privacy Policy explains how we collect,
                  use, disclose, and safeguard your information when you use our services or visit our website.
                </p>
                <p className="text-gray-300 leading-relaxed mb-4">
                  As a provider of algorithmic trading strategies to financial institutions, we understand the
                  critical importance of data security and privacy in the financial services industry. We adhere
                  to the highest standards of data protection and comply with applicable privacy laws and regulations.
                </p>
                <p className="text-gray-300 leading-relaxed">
                  By using our services, you consent to the collection and use of your information as described
                  in this Privacy Policy. If you do not agree with our policies and practices, please do not use our services.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Policy Sections */}
          {sections.map((section, index) => (
            <Card key={section.id} className="bg-white/5 backdrop-blur-sm border-white/10">
              <CardContent className="p-8">
                <div className="flex items-center space-x-3 mb-6">
                  <section.icon className="w-8 h-8 text-blue-400" />
                  <h2 className="text-2xl font-bold text-white">{section.title}</h2>
                </div>
                <div className="space-y-4">
                  {section.content.map((item, idx) => (
                    <p key={idx} className="text-gray-300 leading-relaxed">
                      {item}
                    </p>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}

          {/* International Transfers */}
          <Card className="bg-white/5 backdrop-blur-sm border-white/10">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-white mb-6">International Data Transfers</h2>
              <div className="space-y-4">
                <p className="text-gray-300 leading-relaxed">
                  As a global organization with offices in multiple jurisdictions, we may transfer your personal
                  information across international borders. We ensure that such transfers comply with applicable
                  data protection laws and implement appropriate safeguards.
                </p>
                <p className="text-gray-300 leading-relaxed">
                  For transfers from the European Economic Area (EEA), we rely on adequacy decisions, standard
                  contractual clauses, or other appropriate safeguards as approved by the European Commission.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card className="bg-white/5 backdrop-blur-sm border-white/10">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Contact Us</h2>
              <div className="space-y-4">
                <p className="text-gray-300 leading-relaxed">
                  If you have any questions about this Privacy Policy or our data practices, please contact us:
                </p>
                <div className="space-y-2 text-gray-300">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Phone:</strong> +4740396880</p>
                  <p><strong>Address:</strong> Øvre Kråkenes 213B, Norway</p>
                </div>
                <p className="text-gray-300 leading-relaxed">
                  For EU residents, you may also contact our Data Protection Officer at: <EMAIL>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Changes to Policy */}
          <Card className="bg-yellow-500/10 border-yellow-500/20">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Changes to This Privacy Policy</h2>
              <div className="space-y-4">
                <p className="text-gray-300 leading-relaxed">
                  We may update this Privacy Policy from time to time to reflect changes in our practices,
                  technology, legal requirements, or other factors. We will notify you of any material changes
                  by posting the updated policy on our website and, where appropriate, by sending you a notification.
                </p>
                <p className="text-gray-300 leading-relaxed">
                  The "Last Updated" date at the top of this policy indicates when it was most recently revised.
                  Your continued use of our services after any changes indicates your acceptance of the updated policy.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </Layout>
  );
};

export default PrivacyPolicy;
