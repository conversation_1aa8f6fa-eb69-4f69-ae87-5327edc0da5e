import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON>u, X, Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";

/**
 * Modern Header component with enhanced glassmorphism and animations
 * Features improved responsive design, better mobile experience, and modern styling
 * Includes smooth transitions and interactive elements
 */
export const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();

  // Navigation items configuration
  const navItems = [
    { path: "/", label: "Home" },
    { path: "/about", label: "About" },
    { path: "/services", label: "Services" },
    { path: "/pricing", label: "Pricing" },
    { path: "/documentation", label: "Documentation" },
    { path: "/contact", label: "Contact" },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  /**
   * Determines if a navigation item is currently active
   * @param path - The path to check against current location
   * @returns boolean indicating if path is active
   */
  const isActivePath = (path: string) => {
    if (path === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(path);
  };

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-500",
        scrolled
          ? "glass-strong border-b border-white/20 shadow-modern-lg"
          : "bg-transparent border-b border-white/5"
      )}
    >
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex items-center justify-between h-20">
          {/* Logo and Brand */}
          <div
            className="flex items-center space-x-3 group cursor-pointer"
            onClick={() => {
              // Always scroll to top when clicking logo
              window.scrollTo({ top: 0, behavior: 'smooth' });
              // Navigate to home if not already there
              if (location.pathname !== "/") {
                window.location.href = "/";
              }
            }}
          >
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-purple-500 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-glow group-hover:shadow-glow-purple">
                <Brain className="w-7 h-7 text-white group-hover:animate-pulse" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                <Sparkles className="w-2 h-2 text-white" />
              </div>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-black text-white group-hover:text-gradient-blue transition-all duration-300">
                FoxAI
              </span>
              <span className="text-xs font-medium text-gray-400 group-hover:text-blue-400 transition-colors duration-300 -mt-1">
                Trading
              </span>
            </div>
          </div>

          {/* Refined Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-2">
            {navItems.map((item) => {
              if (item.path === "/") {
                // Special handling for Home button
                return (
                  <button
                    key={item.path}
                    onClick={() => {
                      window.scrollTo({ top: 0, behavior: 'smooth' });
                      if (location.pathname !== "/") {
                        window.location.href = "/";
                      }
                    }}
                    className={cn(
                      "relative px-5 py-3 text-sm font-semibold transition-all duration-300 rounded-xl group hover-lift",
                      isActivePath(item.path)
                        ? "text-white glass-subtle shadow-glow-subtle"
                        : "text-gray-300 hover:text-white hover:bg-white/3"
                    )}
                  >
                    {item.label}
                    {isActivePath(item.path) && (
                      <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-400 rounded-full" />
                    )}
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/0 to-purple-500/0 group-hover:from-blue-500/5 group-hover:to-purple-500/5 transition-all duration-500" />
                  </button>
                );
              } else {
                // Regular Link for other navigation items - with scroll to top
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    onClick={() => {
                      // Always scroll to top when navigating to any page
                      setTimeout(() => {
                        window.scrollTo({ top: 0, behavior: 'instant' });
                      }, 10);
                    }}
                    className={cn(
                      "relative px-5 py-3 text-sm font-semibold transition-all duration-300 rounded-xl group hover-lift",
                      isActivePath(item.path)
                        ? "text-white glass-subtle shadow-glow-subtle"
                        : "text-gray-300 hover:text-white hover:bg-white/3"
                    )}
                  >
                    {item.label}
                    {isActivePath(item.path) && (
                      <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-400 rounded-full" />
                    )}
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/0 to-purple-500/0 group-hover:from-blue-500/5 group-hover:to-purple-500/5 transition-all duration-500" />
                  </Link>
                );
              }
            })}
          </nav>

          {/* Refined Desktop CTA Button */}
          <div className="hidden lg:block">
            <Link
              to="/contact"
              onClick={() => {
                // Always scroll to top when navigating to contact
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'instant' });
                }, 10);
              }}
            >
              <Button className="group bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-size-200 bg-pos-0 hover:bg-pos-100 text-white px-8 py-3 font-semibold rounded-2xl transition-all duration-500 hover-lift shadow-glow-subtle hover:shadow-glow">
                <span className="relative z-10">Get Started</span>
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-600/0 via-pink-500/0 to-purple-600/0 group-hover:from-purple-600/10 group-hover:via-pink-500/10 group-hover:to-purple-600/10 transition-all duration-500" />
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-3 text-gray-300 hover:text-white transition-all duration-300 rounded-xl hover:bg-white/10"
            aria-label="Toggle mobile menu"
          >
            <div className="relative w-6 h-6">
              <Menu
                className={cn(
                  "absolute inset-0 w-6 h-6 transition-all duration-300",
                  isMenuOpen ? "opacity-0 rotate-180" : "opacity-100 rotate-0"
                )}
              />
              <X
                className={cn(
                  "absolute inset-0 w-6 h-6 transition-all duration-300",
                  isMenuOpen ? "opacity-100 rotate-0" : "opacity-0 -rotate-180"
                )}
              />
            </div>
          </button>
        </div>

        {/* Mobile Navigation Menu */}
        <div
          className={cn(
            "lg:hidden overflow-hidden transition-all duration-500 ease-out",
            isMenuOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
          )}
        >
          <div className="py-6 border-t border-white/10">
            <nav className="flex flex-col space-y-2">
              {navItems.map((item, index) => {
                if (item.path === "/") {
                  // Special handling for Home button in mobile
                  return (
                    <button
                      key={item.path}
                      onClick={() => {
                        setIsMenuOpen(false);
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                        if (location.pathname !== "/") {
                          window.location.href = "/";
                        }
                      }}
                      className={cn(
                        "px-4 py-3 text-base font-semibold transition-all duration-300 rounded-xl animate-slide-up w-full text-left",
                        isActivePath(item.path)
                          ? "text-white bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30"
                          : "text-gray-300 hover:text-white hover:bg-white/10"
                      )}
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      {item.label}
                    </button>
                  );
                } else {
                  // Regular Link for other navigation items - with scroll to top
                  return (
                    <Link
                      key={item.path}
                      to={item.path}
                      onClick={() => {
                        setIsMenuOpen(false);
                        // Always scroll to top when navigating to any page
                        setTimeout(() => {
                          window.scrollTo({ top: 0, behavior: 'instant' });
                        }, 10);
                      }}
                      className={cn(
                        "px-4 py-3 text-base font-semibold transition-all duration-300 rounded-xl animate-slide-up",
                        isActivePath(item.path)
                          ? "text-white bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30"
                          : "text-gray-300 hover:text-white hover:bg-white/10"
                      )}
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      {item.label}
                    </Link>
                  );
                }
              })}
              <div className="pt-4 animate-slide-up" style={{ animationDelay: `${navItems.length * 0.1}s` }}>
                <Link
                  to="/contact"
                  onClick={() => {
                    setIsMenuOpen(false);
                    // Always scroll to top when navigating to contact
                    setTimeout(() => {
                      window.scrollTo({ top: 0, behavior: 'instant' });
                    }, 10);
                  }}
                >
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-4 text-base font-semibold rounded-2xl transition-all duration-300 shadow-glow">
                    Get Started
                  </Button>
                </Link>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
};
