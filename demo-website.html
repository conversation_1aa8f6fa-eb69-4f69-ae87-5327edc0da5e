<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoxAI - Demo Website</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .hero { min-height: 100vh; display: flex; align-items: center; text-align: center; }
        .hero h1 { font-size: 4rem; font-weight: 900; margin-bottom: 2rem; }
        .hero .highlight { color: #10b981; }
        .hero p { font-size: 1.5rem; margin-bottom: 3rem; opacity: 0.9; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin: 4rem 0; }
        .stat-card { 
            background: rgba(255,255,255,0.1); 
            padding: 2rem; 
            border-radius: 1rem; 
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .stat-number { font-size: 3rem; font-weight: 900; color: #10b981; margin-bottom: 0.5rem; }
        .stat-label { opacity: 0.8; }
        .testimonials { margin: 4rem 0; }
        .testimonial-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .testimonial { 
            background: rgba(255,255,255,0.1); 
            padding: 2rem; 
            border-radius: 1rem;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .testimonial-quote { font-style: italic; margin-bottom: 1rem; }
        .testimonial-author { font-weight: bold; }
        .testimonial-title { opacity: 0.8; font-size: 0.9rem; }
        .testimonial-result { color: #10b981; font-weight: bold; margin-top: 0.5rem; }
        .btn { 
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: bold;
            margin: 1rem;
            transition: transform 0.2s;
        }
        .btn:hover { transform: translateY(-2px); }
        .section { padding: 4rem 0; }
        .section h2 { font-size: 3rem; text-align: center; margin-bottom: 3rem; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Hero Section -->
        <section class="hero">
            <div>
                <h1>Stop Losing to <span class="highlight">AI-Powered</span> Competitors</h1>
                <p>While other traders gain <span class="highlight">592% average returns</span> with our exclusive AI strategies, you're still using outdated methods.</p>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">$1M+</div>
                        <div class="stat-label">Under Management</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">592%</div>
                        <div class="stat-label">Average Client ROI</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Individual Traders</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">48hrs</div>
                        <div class="stat-label">Average Setup Time</div>
                    </div>
                </div>
                
                <a href="#" class="btn" onclick="showROIPopup()">Get Free ROI Analysis</a>
                <a href="#" class="btn">Secure Your License Now</a>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="section testimonials">
            <h2>Individual Trader Success Stories</h2>
            <div class="testimonial-grid">
                <div class="testimonial">
                    <div class="testimonial-quote">"My portfolio exploded by 180% in just 6 months. This isn't just software - it's a money-making machine."</div>
                    <div class="testimonial-author">Sarah Chen</div>
                    <div class="testimonial-title">Independent Day Trader</div>
                    <div class="testimonial-title">$75K Account</div>
                    <div class="testimonial-result">180% ROI increase in 6 months</div>
                </div>
                
                <div class="testimonial">
                    <div class="testimonial-quote">"While other traders struggled with 12% returns, I achieved 245% using their Quantum Trading strategy."</div>
                    <div class="testimonial-author">Marcus Rodriguez</div>
                    <div class="testimonial-title">Swing Trader</div>
                    <div class="testimonial-title">$50K Account</div>
                    <div class="testimonial-result">245% annual return in 12 months</div>
                </div>
                
                <div class="testimonial">
                    <div class="testimonial-quote">"The setup was seamless. I was profitable within 48 hours and made 320% in 8 months."</div>
                    <div class="testimonial-author">Aisha Patel</div>
                    <div class="testimonial-title">Part-Time Trader</div>
                    <div class="testimonial-title">$100K Account</div>
                    <div class="testimonial-result">320% in 8 months</div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="section">
            <h2>Contact FoxAI</h2>
            <div style="text-align: center;">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> +**********</p>
                <p><strong>Address:</strong> Øvre Kråkenes 213B, Norway</p>
                <p style="margin-top: 2rem; opacity: 0.8;">© FoxAI 2025</p>
            </div>
        </section>
    </div>

    <!-- Simple ROI Popup -->
    <div id="roiPopup" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #1e293b; padding: 3rem; border-radius: 1rem; max-width: 500px; width: 90%;">
            <h3 style="margin-bottom: 1rem; color: #10b981;">Get Your FREE ROI Analysis</h3>
            <p style="margin-bottom: 2rem;">Discover Your Potential 592% Returns in Under 60 Seconds</p>
            <form>
                <input type="text" placeholder="Full Name" style="width: 100%; padding: 1rem; margin-bottom: 1rem; border-radius: 0.5rem; border: none; background: rgba(255,255,255,0.1); color: white;">
                <input type="email" placeholder="Email Address" style="width: 100%; padding: 1rem; margin-bottom: 1rem; border-radius: 0.5rem; border: none; background: rgba(255,255,255,0.1); color: white;">
                <input type="text" placeholder="Trading Capital (e.g., $50,000)" style="width: 100%; padding: 1rem; margin-bottom: 2rem; border-radius: 0.5rem; border: none; background: rgba(255,255,255,0.1); color: white;">
                <button type="submit" style="width: 100%; padding: 1rem; background: linear-gradient(135deg, #10b981, #059669); color: white; border: none; border-radius: 0.5rem; font-weight: bold; cursor: pointer;">🚀 Get My FREE ROI Analysis Now</button>
            </form>
            <button onclick="hideROIPopup()" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">×</button>
        </div>
    </div>

    <script>
        function showROIPopup() {
            document.getElementById('roiPopup').style.display = 'block';
        }
        function hideROIPopup() {
            document.getElementById('roiPopup').style.display = 'none';
        }
    </script>
</body>
</html>
